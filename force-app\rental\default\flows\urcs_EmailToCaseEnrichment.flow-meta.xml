<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>59.0</apiVersion>
    <description>Screen Flow per arricchimento Case creati da Email-to-Case</description>
    <dynamicChoiceSets>
        <name>CategoriaChoices</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Categoria__c</picklistField>
        <picklistObject>Case</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>SottoCategoriaChoices</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>SottoCategoria__c</picklistField>
        <picklistObject>Case</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>EntChiamanteChoices</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>EntChiamante__c</picklistField>
        <picklistObject>Case</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <interviewLabel>Email-to-Case Enrichment {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_EmailToCaseEnrichment</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>GetCaseRecord</name>
        <label>Get Case Record</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CheckCaseEligibility</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetContractAssets</name>
        <label>Get Contract Assets</label>
        <locationX>176</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ScreenVeicoloContratto</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ServiceContract__r.Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>selectedAccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ContractAsset__c</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Name</queriedFields>
        <queriedFields>Asset__r.Name</queriedFields>
        <queriedFields>Asset__r.Targa__c</queriedFields>
        <queriedFields>ServiceContract__r.ContractNumber</queriedFields>
        <queriedFields>ServiceContract__r.Account__r.Name</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>UpdateCaseRecord</name>
        <label>Update Case Record</label>
        <locationX>176</locationX>
        <locationY>1322</locationY>
        <connector>
            <targetReference>ScreenConfermaCompletamento</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>selectedAccountId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Categoria__c</field>
            <value>
                <elementReference>selectedCategoria</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>selectedContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContractAsset__c</field>
            <value>
                <elementReference>selectedContractAssetId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>DriverId__c</field>
            <value>
                <elementReference>selectedDriverId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>EntChiamante__c</field>
            <value>
                <elementReference>selectedEntChiamante</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>SottoCategoria__c</field>
            <value>
                <elementReference>selectedSottoCategoria</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <decisions>
        <name>CheckCaseEligibility</name>
        <label>Check Case Eligibility</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>ScreenCaseNonEligibile</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Case Non Eligibile</defaultConnectorLabel>
        <rules>
            <name>CaseEligibile</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCaseRecord.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Email</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GetCaseRecord.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Nuova richiesta</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ScreenCategoriaEEntita</targetReference>
            </connector>
            <label>Case Eligibile</label>
        </rules>
    </decisions>
    <screens>
        <name>ScreenCaseNonEligibile</name>
        <label>Case Non Eligibile</label>
        <locationX>374</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>TextCaseNonEligibile</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68); font-size: 16px;&quot;&gt;Questo Case non è eligibile per l&apos;enrichment.&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Solo i Case creati da Email-to-Case con stato &quot;Nuova richiesta&quot; possono essere arricchiti.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>ScreenCategoriaEEntita</name>
        <label>Categoria e Entità Chiamante</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>false</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>ScreenAccountEContact</targetReference>
        </connector>
        <fields>
            <name>HeaderCategoria</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68); font-size: 18px;&quot;&gt;&lt;strong&gt;Arricchimento Case Email-to-Case&lt;/strong&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Seleziona la categoria e l&apos;entità chiamante&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>selectedCategoria</name>
            <choiceReferences>CategoriaChoices</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Categoria</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>selectedSottoCategoria</name>
            <choiceReferences>SottoCategoriaChoices</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Sottocategoria</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>selectedEntChiamante</name>
            <choiceReferences>EntChiamanteChoices</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Entità Chiamante</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Prosegui</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>ScreenAccountEContact</name>
        <label>Account e Contact</label>
        <locationX>176</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>false</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Indietro</backButtonLabel>
        <connector>
            <targetReference>GetContractAssets</targetReference>
        </connector>
        <fields>
            <name>HeaderAccount</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68); font-size: 16px;&quot;&gt;&lt;strong&gt;Selezione Account e Referente&lt;/strong&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Seleziona l&apos;account e il referente per il case&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>selectedAccountId</name>
            <extensionName>flowruntime:lookup</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>fieldApiName</name>
                <value>
                    <stringValue>AccountId</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Nome Account</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>objectApiName</name>
                <value>
                    <stringValue>Account</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>required</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>selectedContactId</name>
            <extensionName>flowruntime:lookup</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>fieldApiName</name>
                <value>
                    <stringValue>ContactId</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Nome Referente</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>objectApiName</name>
                <value>
                    <stringValue>Contact</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>required</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <isRequired>false</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Prosegui</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>ScreenVeicoloContratto</name>
        <label>Veicolo e Contratto</label>
        <locationX>176</locationX>
        <locationY>674</locationY>
        <allowBack>true</allowBack>
        <allowFinish>false</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Indietro</backButtonLabel>
        <connector>
            <targetReference>AssignSelectedContractAsset</targetReference>
        </connector>
        <fields>
            <name>HeaderVeicolo</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68); font-size: 16px;&quot;&gt;&lt;strong&gt;Selezione Veicolo del Contratto&lt;/strong&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Seleziona il veicolo del contratto (l&apos;utilizzatore verrà popolato automaticamente)&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>VeicoliTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>ContractAsset__c</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Veicoli Disponibili</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>GetContractAssets</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{"label":"Nome","fieldName":"Name","type":"text"},{"label":"Veicolo","fieldName":"Asset__r.Name","type":"text"},{"label":"Targa","fieldName":"Asset__r.Targa__c","type":"text"},{"label":"Contratto","fieldName":"ServiceContract__r.ContractNumber","type":"text"}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <outputParameters>
                <assignToReference>selectedContractAssets</assignToReference>
                <name>selectedRows</name>
            </outputParameters>
        </fields>
        <nextOrFinishButtonLabel>Prosegui</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <assignments>
        <name>AssignSelectedContractAsset</name>
        <label>Assign Selected Contract Asset</label>
        <locationX>176</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>selectedContractAssetId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>selectedContractAssets.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>CheckIfDriverExists</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>CheckIfDriverExists</name>
        <label>Check If Driver Exists</label>
        <locationX>176</locationX>
        <locationY>890</locationY>
        <defaultConnector>
            <targetReference>ScreenRiepilogo</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Driver Found</defaultConnectorLabel>
        <rules>
            <name>HasContractAsset</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>selectedContractAssetId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetDriverForContractAsset</targetReference>
            </connector>
            <label>Has Contract Asset</label>
        </rules>
    </decisions>
    <recordLookups>
        <name>GetDriverForContractAsset</name>
        <label>Get Driver for Contract Asset</label>
        <locationX>44</locationX>
        <locationY>998</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>AssignDriverFromContractAsset</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ContractAsset__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>selectedContractAssetId</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsPrimary__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ContractAssetDriver__c</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Contact__c</queriedFields>
        <queriedFields>Contact__r.Name</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <assignments>
        <name>AssignDriverFromContractAsset</name>
        <label>Assign Driver from Contract Asset</label>
        <locationX>44</locationX>
        <locationY>1106</locationY>
        <assignmentItems>
            <assignToReference>selectedDriverId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetDriverForContractAsset.Contact__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>ScreenRiepilogo</targetReference>
        </connector>
    </assignments>
    <screens>
        <name>ScreenRiepilogo</name>
        <label>Riepilogo Selezioni</label>
        <locationX>176</locationX>
        <locationY>1214</locationY>
        <allowBack>true</allowBack>
        <allowFinish>false</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Indietro</backButtonLabel>
        <connector>
            <targetReference>UpdateCaseRecord</targetReference>
        </connector>
        <fields>
            <name>HeaderRiepilogo</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68); font-size: 16px;&quot;&gt;&lt;strong&gt;Riepilogo delle Selezioni&lt;/strong&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Verifica i dati selezionati prima di confermare&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>RiepilogoCategoria</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;Categoria:&lt;/strong&gt; {!selectedCategoria}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>RiepilogoSottoCategoria</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;Sottocategoria:&lt;/strong&gt; {!selectedSottoCategoria}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>RiepilogoEntChiamante</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;Entità Chiamante:&lt;/strong&gt; {!selectedEntChiamante}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>RiepilogoAccount</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;Account:&lt;/strong&gt; {!selectedAccountId}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>RiepilogoContact</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;Referente:&lt;/strong&gt; {!selectedContactId}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>RiepilogoVeicolo</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;Veicolo Contratto:&lt;/strong&gt; {!selectedContractAssetId}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>RiepilogoDriver</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;Utilizzatore:&lt;/strong&gt; {!GetDriverForContractAsset.Contact__r.Name}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Conferma e Salva</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>ScreenConfermaCompletamento</name>
        <label>Conferma Completamento</label>
        <locationX>176</locationX>
        <locationY>1430</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>TextCompletamento</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68); font-size: 16px;&quot;&gt;&lt;strong&gt;✅ Arricchimento Completato&lt;/strong&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Il Case è stato arricchito con successo e può ora essere lavorato.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Chiudi</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetCaseRecord</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>selectedCategoria</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>selectedSottoCategoria</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>selectedEntChiamante</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>selectedAccountId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>selectedContactId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>selectedContractAssetId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>selectedDriverId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>selectedContractAssets</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>ContractAsset__c</objectType>
    </variables>
</Flow>
