<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>urcs_EmailToCaseEnrichmentRequired</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
    NOT(ISNEW()),
    ISPICKVAL(Origin, "Email"),
    ISPICKVAL(Status, "Nuova richiesta"),
    OR(
        ISBLANK(TEXT(Categoria__c)),
        ISBLANK(AccountId),
        ISBLANK(TEXT(EntChiamante__c))
    ),
    NOT(ISCHANGED(Categoria__c)),
    NOT(ISCHANGED(AccountId)),
    NOT(ISCHANGED(EntChiamante__c)),
    NOT(ISCHANGED(SottoCategoria__c)),
    NOT(ISCHANGED(ContactId)),
    NOT(ISCHANGED(ContractAsset__c)),
    NOT(ISCHANGED(DriverId__c))
)</errorConditionFormula>
    <errorMessage><PERSON><PERSON> Case da Email-to-Case deve essere arricchito prima di poter essere modificato. Utilizza il pulsante "Arricchisci Case Email" per completare le informazioni mancanti.</errorMessage>
</ValidationRule>
